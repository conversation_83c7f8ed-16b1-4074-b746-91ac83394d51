package com.rutong.platform.client.service;

import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.fastjson.JSONArray;
import com.rutong.framework.bean.GridParams;
import com.rutong.framework.bean.PageInfo;
import com.rutong.framework.bean.ResultBean;
import com.rutong.framework.core.jpa.FilterCondition;
import com.rutong.framework.core.jpa.SortCondition;
import com.rutong.framework.security.model.LoginUser;
import com.rutong.framework.utils.BeanMapUtils;
import com.rutong.framework.utils.SecurityUtils;
import com.rutong.framework.utils.SqlQueryBuilder;
import com.rutong.platform.client.annotation.ChangeTracking;
import com.rutong.platform.client.entity.*;
import com.rutong.platform.common.pojo.TreeData;
import com.rutong.platform.common.service.BaseService;
import javassist.expr.NewArray;
import lombok.RequiredArgsConstructor;
import lombok.val;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.StatelessSession;
import org.hibernate.Transaction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;

@RequiredArgsConstructor
@Service
public class EmployeeProductConfigurationService extends BaseService<EmployeeProductConfiguration> {
    @Autowired
    private ClientInfoService clientInfoService;
    @Autowired
    private EmployeeProductConfigurationService employeeProductConfigurationService;
    @Autowired
    private EmployeeProductConfigurationDetailService employeeProductConfigurationDetailService;

    @Autowired
    private ChangeTrackingService changeTrackingService;

    public static final String EMPLOYEE_ID = "employeeId";

    public BigInteger countByContractNumber(String contractNumber) {
        String sql = "SELECT COUNT(*) FROM employee_product_configuration WHERE contract_number = ?0 ";
        return dao.countSQLQuery(sql, contractNumber);
    }

    public List<EmployeeProductConfiguration> findByContractNumber(String contractNumber) {
        return dao.findByProperty(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_COL_CONTRACT_NUMBER, contractNumber);
    }

    public EmployeeProductConfiguration byRfidAndClientId(String rfid, String clientId) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER, rfid, "enterpriseId", clientId, "status", "合作中");
    }


    /**
     * 根据RFID标签编码查询员工服务记录信息
     *
     * @param rfid RFID标签编码
     * @return 员工产品配置信息
     */
    public EmployeeProductConfiguration byRfid(String rfid) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_RFID_TAG_NUMBER, rfid, "status", "合作中");
    }

    /**
     * 根据产品序列号查询员工服务记录信息
     *
     * @param productSerialNumber
     * @return
     */
    public EmployeeProductConfiguration byProductSerialNumber(String productSerialNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                EmployeeProductConfiguration.FIELD_COL_PRODUCT_SERIAL_NUMBER, productSerialNumber);
    }

    /**
     * 根据工作服RFID标签编码查询员工服务记录信息
     *
     * @param rfidTagNumberWork 工作服RFID标签编码
     * @return 员工产品配置信息
     */
    public EmployeeProductConfiguration findByRfidTagNumberWork(String rfidTagNumberWork) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class,
                "rfidTagNumberWork", rfidTagNumberWork, "status", "合作中");
    }

    /**
     * 根据员工手机号查询员工服务记录信息
     *
     * @param employeePhone
     * @return
     */
    public List<EmployeeProductConfiguration> findByEmployeePhone(String employeePhone) {
        return dao.findByProperty(EmployeeProductConfiguration.class,
                "employeePhone", employeePhone);
    }

    /**
     * 根据员工id查询员工服务记录信息
     *
     * @param employeeId
     * @return
     */
    public List<EmployeeProductConfiguration> findByEmployeeId(String employeeId) {
        if (StringUtils.isEmpty(employeeId)) {
            return new ArrayList<>();
        }
        return dao.findByProperty(EmployeeProductConfiguration.class, EMPLOYEE_ID, employeeId, "status", "合作中");
    }

    /**
     * 批量保存员工服务记录信息 - 高性能优化版本
     *
     * @param dataList
     * @param contractNumber
     * @return
     */
    @Transactional
    public Map<String, Object> importData(List<EmployeeProductConfiguration> dataList, String contractNumber) {
        long startTime = System.currentTimeMillis();
        System.out.println("开始高性能批量导入，数据量: " + dataList.size());

        Map<String, Object> result = new HashMap<>();
        List<Map<String, String>> errors = new ArrayList<>();

        ClientInfo clientInfo = clientInfoService.findByContractNumber(contractNumber);
        if (clientInfo == null) {
            errors.add(createError(0, "全局错误", "找不到对应的合同信息"));
            result.put("errors", errors);
            return result;
        }

        // 1. 批量查询现有数据，构建索引Map
        long queryStart = System.currentTimeMillis();
        List<EmployeeProductConfiguration> dataAll = employeeProductConfigurationService.findByContractNumber(contractNumber);
        Map<String, EmployeeProductConfiguration> existingRecords = new HashMap<>();
        for (EmployeeProductConfiguration record : dataAll) {
            if (record.getRfidTagNumberWork() != null) {
                existingRecords.put(record.getRfidTagNumberWork(), record);
            }
        }
        System.out.println("查询现有数据耗时: " + (System.currentTimeMillis() - queryStart) + "ms");

        // 2. 数据校验与分类
        List<EmployeeProductConfiguration> toInsert = new ArrayList<>();
        List<EmployeeProductConfiguration> toUpdate = new ArrayList<>();
        List<EmployeeProductConfiguration> changedRecords = new ArrayList<>(); // 用于变更追踪

        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date currentTime = new Date();

        for (int i = 0; i < dataList.size(); i++) {
            int rowNumber = i + 2; // Excel行号从2开始
            EmployeeProductConfiguration config = dataList.get(i);
            List<String> errorDetails = validateConfig(config, rowNumber);

            if (!errorDetails.isEmpty()) {
                errors.add(createError(rowNumber, "多字段错误",
                        "本行包含" + errorDetails.size() + "个错误：" + String.join("；", errorDetails)));
                continue;
            }

            // 设置公共字段
            config.setContractNumber(contractNumber);
            config.setStatus("合作中");
            config.setEnterpriseName(clientInfo.getEnterpriseName());
            config.setEnterpriseId(clientInfo.getId());

            // 判断是新增还是更新
            EmployeeProductConfiguration existing = existingRecords.get(config.getRfidTagNumberWork());
            if (existing != null) {
                // 检查是否真的有变更，避免无意义的更新
                if (hasActualChanges(config, existing)) {
                    BeanUtils.copyProperties(config, existing, "id", "createTime", "createBy");
                    existing.setUpdateTime(currentTime);
                    if (loginUser != null) {
                        existing.setUpdateBy(loginUser.getUsername());
                    }
                    toUpdate.add(existing);
                    changedRecords.add(config); // 记录变更的数据用于历史追踪
                }
                // 如果没有变更，则跳过更新
            } else {
                if (loginUser != null) {
                    config.setCreateBy(loginUser.getUsername());
                    config.setOwnerDeptId(loginUser.getDeptId());
                    config.setOwnerUserId(loginUser.getUserId());
                }
                config.setCreateTime(currentTime);
                toInsert.add(config);
            }
        }

        System.out.println("数据分类完成 - 新增: " + toInsert.size() + ", 更新: " + toUpdate.size());

        // 3. 批量操作
        try {
            // 批量插入新记录
            if (!toInsert.isEmpty()) {
                long insertStart = System.currentTimeMillis();
                batchInsertWithDetails(toInsert);
                System.out.println("批量插入耗时: " + (System.currentTimeMillis() - insertStart) + "ms");
            }

//            // 批量更新现有记录
//            if (!toUpdate.isEmpty()) {
//                long updateStart = System.currentTimeMillis();
//                batchUpdateWithDetails(toUpdate);
//                System.out.println("批量更新耗时: " + (System.currentTimeMillis() - updateStart) + "ms");
//            }

            // 4. 异步处理变更追踪（仅对有变更的记录）
            if (!changedRecords.isEmpty()) {
                long trackingStart = System.currentTimeMillis();
                asyncTrackChanges(changedRecords);
                System.out.println("启动变更追踪耗时: " + (System.currentTimeMillis() - trackingStart) + "ms");
            }

            // 5. 更新合同统计信息
            BigInteger count = employeeProductConfigurationService.countByContractNumber(clientInfo.getContractNumber());
            clientInfo.setCountData(count.longValue());
            clientInfoService.save(clientInfo);

            // 返回结果
            result.put("errorList", errors);
            result.put("successCount", toInsert.size() + toUpdate.size());
            result.put("newCount", toInsert.size());
            result.put("updateCount", toUpdate.size());

            long totalTime = System.currentTimeMillis() - startTime;
            System.out.println("批量导入完成，总耗时: " + totalTime + "ms");

            return result;
        } catch (Exception e) {
            System.err.println("批量导入失败: " + e.getMessage());
            e.printStackTrace();
            throw new RuntimeException("数据保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查两个实体是否有实际变更
     */
    private boolean hasActualChanges(EmployeeProductConfiguration newEntity, EmployeeProductConfiguration existing) {
        // 获取实体类上的变更追踪注解
        ChangeTracking tracking = newEntity.getClass().getAnnotation(ChangeTracking.class);
        if (tracking == null) {
            return false; // 如果没有注解，默认不追踪变更
        }

        // 获取需要忽略的字段集合
        Set<String> ignoreFields = new HashSet<>(Arrays.asList(tracking.ignoreFields()));

        // 获取实体类的所有字段（包括父类）
        List<Field> allFields = getAllFields(newEntity.getClass());

        // 遍历所有字段进行比较
        for (Field field : allFields) {
            String fieldName = field.getName();

            // 跳过忽略的字段
            if (ignoreFields.contains(fieldName)) {
                continue;
            }

            try {
                field.setAccessible(true);
                Object newValue = field.get(newEntity);
                Object oldValue = field.get(existing);

                // 如果发现字段值不同，则返回true表示有变更
                if (!Objects.equals(newValue, oldValue)) {
                    return true;
                }
            } catch (IllegalAccessException e) {
                // 忽略无法访问的字段
                continue;
            }
        }

        return false; // 所有字段都相同
    }

    /**
     * 获取类及其父类的所有字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 批量插入记录及其详情
     */
    private void batchInsertWithDetails(List<EmployeeProductConfiguration> toInsert) {
        // 使用批量保存主记录
        for (EmployeeProductConfiguration record : toInsert) {
            employeeProductConfigurationService.save(record);

            // 处理子表数据
            if (record.getTableData() != null && !record.getTableData().trim().isEmpty()) {
                try {
                    List<EmployeeProductConfigurationDetail> details = JSONArray.parseArray(
                        record.getTableData(), EmployeeProductConfigurationDetail.class);
                    if (details != null && !details.isEmpty()) {
                        batchSaveDetails(details, record.getId());
                    }
                } catch (Exception e) {
                    System.err.println("处理子表数据失败，记录ID: " + record.getId() + ", 错误: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 批量更新记录及其详情
     */
    private void batchUpdateWithDetails(List<EmployeeProductConfiguration> toUpdate) {
        for (EmployeeProductConfiguration updateEntity : toUpdate) {
            employeeProductConfigurationService.update(updateEntity);

            // 处理子表数据
            if (updateEntity.getTableData() != null && !updateEntity.getTableData().trim().isEmpty()) {
                try {
                    // 先删除旧的详情记录
                    employeeProductConfigurationDetailService.deleteByEmployeeProductConfigurationId(updateEntity.getId());

                    List<EmployeeProductConfigurationDetail> details = JSONArray.parseArray(
                        updateEntity.getTableData(), EmployeeProductConfigurationDetail.class);
                    if (details != null && !details.isEmpty()) {
                        batchSaveDetails(details, updateEntity.getId());
                    }
                } catch (Exception e) {
                    System.err.println("处理子表数据失败，记录ID: " + updateEntity.getId() + ", 错误: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 批量保存详情记录
     */
    private void batchSaveDetails(List<EmployeeProductConfigurationDetail> details, String parentId) {
        LoginUser loginUser = SecurityUtils.getLoginUser();
        Date currentTime = new Date();

        for (EmployeeProductConfigurationDetail detail : details) {
            detail.setEmployeeProductConfigurationId(parentId);
            detail.setId(UUID.randomUUID().toString());

            if (loginUser != null) {
                detail.setCreateBy(loginUser.getUsername());
                detail.setOwnerDeptId(loginUser.getDeptId());
                detail.setOwnerUserId(loginUser.getUserId());
            }
            detail.setCreateTime(currentTime);

            employeeProductConfigurationDetailService.safeSave(detail);
        }
    }

    /**
     * 异步处理变更追踪
     */
    private void asyncTrackChanges(List<EmployeeProductConfiguration> changedRecords) {
        // 使用异步线程处理变更追踪，避免阻塞主流程
        new Thread(() -> {
            try {
                System.out.println("开始异步处理变更追踪，记录数: " + changedRecords.size());
                changeTrackingService.batchImportTrackChanges(changedRecords);
                System.out.println("异步变更追踪处理完成");
            } catch (Exception e) {
                System.err.println("异步变更追踪处理失败: " + e.getMessage());
                e.printStackTrace();
            }
        }, "ImportChangeTracking-" + System.currentTimeMillis()).start();
    }

    // 验证方法
    private List<String> validateConfig(EmployeeProductConfiguration config, int rowNumber) {
        List<String> errors = new ArrayList<>();

        // 验证每周换洗日期
        if (!validateWeeklyWashDate(config.getWeeklyWashDate())) {
            errors.add("每周换洗日期格式不正确，需为1,2,3,4,5,6,7格式");
        }

        // 验证单次洗涤租赁费用
        if (StringUtils.isEmpty(config.getSingleWashRentalFee())) {
            errors.add("单次洗涤租赁费用不能为空");
        }

        // 检查工作服RFID标签是否为空
        if (StringUtils.isEmpty(config.getRfidTagNumberWork())) {
            errors.add("工作服RFID标签编码不能为空");
        }

        return errors;
    }

    private final EntityManagerFactory entityManagerFactory;

    @Transactional
    public void batchUpdate(List<EmployeeProductConfiguration> updateList) {
        if (updateList.isEmpty()) return;

        StatelessSession session = entityManagerFactory.unwrap(SessionFactory.class).openStatelessSession();
        try {
            session.setJdbcBatchSize(1000);

            for (EmployeeProductConfiguration entity : updateList) {
                session.update(entity);
            }
        } finally {
            session.close();
        }
    }

    public static Map<String, String> createError(int row, String type, String message) {
        Map<String, String> error = new HashMap<>();
        error.put("row", String.valueOf(row));
        error.put("type", type);
        error.put("message", message);
        return error;
    }

    private boolean validateWeeklyWashDate(String date) {
        return date != null && date.matches("^([1-7],)*[1-7]$");
    }

    // Service 层新增方法
    public Map<String, Set<String>> getAllExisting() {
        List<EmployeeProductConfiguration> all = employeeProductConfigurationService.findAll();
        Map<String, Set<String>> map = new HashMap<>();
        map.put("rfidNumberList", all.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumber)
                .collect(Collectors.toSet()));
        map.put("productSerialNumberList", all.stream()
                .map(EmployeeProductConfiguration::getProductSerialNumber)
                .collect(Collectors.toSet()));
        map.put("rfidNumberWorkList", all.stream()
                .map(EmployeeProductConfiguration::getRfidTagNumberWork)
                .collect(Collectors.toSet()));
        return map;
    }

    public Set<String> getAllExistingSerials() {
        return employeeProductConfigurationService.findAll()
                .stream()
                .map(EmployeeProductConfiguration::getProductSerialNumber)
                .collect(Collectors.toSet());
    }

    /**
     * 根据合同编号查询员工产品配置信息，并按员工姓名、部门等字段分组。
     * 通过SQL查询结果转换为EmployeeProductConfiguration对象列表返回，用于前端展示
     *
     * @param contractNumber
     * @return
     */
    public List<EmployeeProductConfiguration> groupDataInfo(String contractNumber) {

        /*employee_name,
        department,
        sub_department,
        work_wear_configuration_category,
        configuration_category
        这样命名之后下划线后第一位字母在返回前端时会将首字母大写*/
        String sql = "select * from employee_product_configuration where contract_number = '" + contractNumber +
                "' group by employee_name, department, sub_department, work_wear_configuration_category, configuration_category";
        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        List<Map<String, Object>> list = dao.executeSQLQuery(sql);
        for (Map<String, Object> map : list) {
            try {
                //使用BeanMapUtils.mapToBean()方法，将数据库查询结果的Map对象转换为EmployeeProductConfiguration实体类对象。
                //通过反射机制，将Map中的键值对映射到实体类的属性中，实现数据封装
                EmployeeProductConfiguration employeeProductConfiguration = BeanMapUtils.mapToBean(map, EmployeeProductConfiguration.class);
                employeeProductConfigurationList.add(employeeProductConfiguration);
            } catch (Exception e) {
                // 打印异常的堆栈跟踪信息，用于调试和错误诊断
                e.printStackTrace();
            }
        }
        return employeeProductConfigurationList;
    }

    public PageInfo<EmployeeProductConfiguration> fetchdataGroup(GridParams gridParams, List<FilterCondition> filters, List<SortCondition> sorts) {

        StringBuilder sqlBuilder = new StringBuilder("SELECT COUNT(*)  FROM (  SELECT    employee_name,     department,      sub_department,    work_wear_configuration_category,    configuration_category  FROM employee_product_configuration ");

        StringBuilder sqlBuilderQu = new StringBuilder("SELECT *,count(rfid_tag_number) as count_info FROM employee_product_configuration");

// 处理 WHERE 子句
        String whereClauseQu = SqlQueryBuilder.buildWhereClause(filters);
        if (!whereClauseQu.isEmpty()) {
            sqlBuilderQu.append(" WHERE ").append(whereClauseQu);
        }
        // 处理 WHERE 子句
        String whereClause = SqlQueryBuilder.buildWhereClause(filters);
        if (!whereClause.isEmpty()) {
            sqlBuilder.append(" WHERE ").append(whereClause);
        }
        sqlBuilder.append(" GROUP BY employee_id) AS grouped_results");
        BigInteger count = dao.countSQLQuery(sqlBuilder.toString());
        List<Map<String, Object>> dataList = new ArrayList();

        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        if (count.longValue() > 0L) {
            sqlBuilderQu.append(" group by employee_id limit " + gridParams.getStart() + "," + gridParams.getPageSize() + " ");
            dataList = dao.executeSQLQuery(sqlBuilderQu.toString());
            for (Map<String, Object> map : dataList) {
                try {
                    EmployeeProductConfiguration employeeProductConfiguration = BeanMapUtils.mapToBean(map, EmployeeProductConfiguration.class);
                    employeeProductConfigurationList.add(employeeProductConfiguration);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return new PageInfo<EmployeeProductConfiguration>(count.longValue(), employeeProductConfigurationList);
    }


    public EmployeeProductConfiguration selectUserByPhone(String phoneNumber) {
        return dao.findByPropertyFirst(EmployeeProductConfiguration.class, EmployeeProductConfigurationHistory.EMPLOYEE_PHONE, phoneNumber);
    }


    /**
     * 根据工作服rfid标签编码计算打印次数
     *
     * @param rfidTagNumberWork
     * @return
     */
    public int countByRfidCode(String rfidTagNumberWork) {
        // 判断rfidCode是否为空或空白
        if (org.apache.commons.lang3.StringUtils.isBlank(rfidTagNumberWork)) {
            // 如果为空或空白，则返回0
            return 0;
        }
        //统计print_record表中rfid_code等于指定值的记录数量。
        // 其中?0是一个占位符，将在执行时被传入的rfidCode参数替换
        String sql = "SELECT COUNT(*) FROM print_record WHERE rfid_tag_number_work = ?0";
        //使用dao对象的countSQLQuery方法，传入sql和rfidCode参数，获取查询结果
        BigInteger count = dao.countSQLQuery(sql, rfidTagNumberWork);
        //如果count不为null，则返回count的整数值，否则返回0
        return count != null ? count.intValue() : 0;
    }

    public void updateNoUser(EmployeeProductConfiguration config) {
        config.setUpdateTime(new Date());
        if (config.getId() == null) {
            // 新实体，使用persist
            dao.persist(config);
        } else {
            // 已存在实体，使用merge
            dao.merge(config);
        }
    }

    public List<EmployeeProductConfiguration> findByIds(List<String> idList) {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        }

        List<EmployeeProductConfiguration> employeeProductConfigurationList = new ArrayList<>();
        // 拼接 IN 语句的值，并确保每个值都被单引号包裹（适用于字符串）
        String inValues = idList.stream()
                .map(id -> "'" + id.replace("'", "''") + "'") // 转义单引号防止 SQL 注入
                .collect(Collectors.joining(","));

        String sql = "from EmployeeProductConfiguration WHERE id IN (" + inValues + ") order by serialNumber";
        List<EmployeeProductConfiguration> list = dao.executeQuery(sql, EmployeeProductConfiguration.class);
        return list;
    }

    /**
     * 根据工作服RFID标签编码列表批量查询
     * @param rfidWorkList RFID工作标签列表
     * @return 员工产品配置列表
     */
    public List<EmployeeProductConfiguration> findByRfidTagNumberWorkIn(List<String> rfidWorkList) {
        if (rfidWorkList == null || rfidWorkList.isEmpty()) {
            return Collections.emptyList();
        }

        // 分批查询，避免IN语句过长
        List<EmployeeProductConfiguration> result = new ArrayList<>();
        int batchSize = 1000; // 每批1000条

        for (int i = 0; i < rfidWorkList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, rfidWorkList.size());
            List<String> batch = rfidWorkList.subList(i, endIndex);

            String inValues = batch.stream()
                    .filter(Objects::nonNull)
                    .map(rfid -> "'" + rfid.replace("'", "''") + "'") // 转义单引号防止 SQL 注入
                    .collect(Collectors.joining(","));

            if (!inValues.isEmpty()) {
                String sql = "from EmployeeProductConfiguration WHERE rfidTagNumberWork IN (" + inValues + ") AND status = '合作中'";
                List<EmployeeProductConfiguration> batchResult = dao.executeQuery(sql, EmployeeProductConfiguration.class);
                result.addAll(batchResult);
            }
        }

        return result;
    }
}
